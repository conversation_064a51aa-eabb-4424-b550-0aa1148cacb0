"use client";

import { <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function NotificationButton() {
  return (
    <Button
      variant="outline"
      size="sm"
      className="h-9 w-9 p-0"
      onClick={() => {
        // Placeholder function - no functionality yet
        console.log("Notification button clicked");
      }}
    >
      <Bell className="h-[1.2rem] w-[1.2rem]" />
      <span className="sr-only">Notifications</span>
    </Button>
  );
}
