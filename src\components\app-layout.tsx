"use client";

import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { Header } from "@/components/header";
import { DynamicBreadcrumb } from "@/components/dynamic-breadcrumb";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex flex-col h-screen">
        <Header />
        <div className="flex flex-1 flex-col min-h-0">
          <div className="px-4 pt-4 flex-shrink-0">
            <DynamicBreadcrumb />
          </div>
          <div className="flex-1 overflow-auto px-4 pt-4 pb-4">
            <div className="space-y-6">{children}</div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
