"use client";

import { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username: string;
}

interface TemplatePreviewModalProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TemplatePreviewModal({
  template,
  open,
  onOpenChange,
}: TemplatePreviewModalProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [error, setError] = useState<string>("");

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleApply = () => {
    // TODO: Implement apply for certificate functionality
    console.log("Apply for certificate:", template?.template_name);
    // For now, just close the modal
    onOpenChange(false);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    setError("");
  };

  const handleImageError = () => {
    setImageLoaded(false);
    setError("Failed to load template preview");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col p-0">
        {/* Template Preview Image */}
        <div className="flex-1 min-h-0 overflow-auto p-6">
          {template && (
            <>
              {/* Loading skeleton while image loads */}
              {!imageLoaded && !error && (
                <div className="space-y-4">
                  <Skeleton className="h-[600px] w-full rounded-lg" />
                </div>
              )}

              {/* Error state */}
              {error && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Template screenshot */}
              <div className="flex justify-center">
                <img
                  src={`/api/templates/${template.id}/screenshot`}
                  alt={`Preview of ${template.template_name}`}
                  className={`max-w-full h-auto rounded-lg shadow-lg border ${
                    imageLoaded ? "block" : "hidden"
                  }`}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex justify-end gap-3 p-6 border-t bg-muted/20">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={!!error}>
            Apply for Certificate
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
