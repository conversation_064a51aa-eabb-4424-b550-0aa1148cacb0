import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

// Enable verbose mode for debugging (can be disabled in production)
const sqlite = sqlite3.verbose();

// Database file path - configurable via environment variable or default to /data/ldis.db
const DB_PATH = process.env.DATABASE_PATH || path.join(process.cwd(), 'data', 'ldis.db');

// Ensure the database directory exists
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Database instance
let db: sqlite3.Database | null = null;

/**
 * Initialize the SQLite database connection
 * Creates the database file if it doesn't exist and sets up tables
 */
export function initializeDatabase(): Promise<sqlite3.Database> {
  return new Promise((resolve, reject) => {
    if (db) {
      // If database is already initialized, still ensure tables exist
      createTables()
        .then(() => resolve(db!))
        .catch(reject);
      return;
    }

    db = new sqlite.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
        reject(err);
        return;
      }

      console.log('Connected to SQLite database at:', DB_PATH);

      // Create tables if they don't exist
      createTables()
        .then(() => resolve(db!))
        .catch(reject);
    });
  });
}

/**
 * Create the users and templates tables if they don't exist
 */
function createTables(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    // Enable foreign keys
    db!.run('PRAGMA foreign_keys = ON', (err) => {
      if (err) {
        console.error('Error enabling foreign keys:', err.message);
        reject(err);
        return;
      }

      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          recovery_key TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      db!.run(createUsersTable, (err) => {
        if (err) {
          console.error('Error creating users table:', err.message);
          reject(err);
          return;
        }

        console.log('Users table created or already exists');

        // Create templates table
        const createTemplatesTable = `
          CREATE TABLE IF NOT EXISTS templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_name TEXT NOT NULL,
            description TEXT,
            filename TEXT NOT NULL,
            placeholders TEXT,
            layout_size TEXT,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
          )
        `;

        db!.run(createTemplatesTable, (err) => {
          if (err) {
            console.error('Error creating templates table:', err.message);
            reject(err);
            return;
          }

          console.log('Templates table created or already exists');
          resolve();
        });
      });
    });
  });
}

/**
 * Get the database instance
 * Initializes the database if not already done
 */
export async function getDatabase(): Promise<sqlite3.Database> {
  if (!db) {
    return await initializeDatabase();
  }
  return db;
}

/**
 * Close the database connection
 */
export function closeDatabase(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!db) {
      resolve();
      return;
    }

    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
        reject(err);
        return;
      }
      
      console.log('Database connection closed');
      db = null;
      resolve();
    });
  });
}

/**
 * Execute a query with parameters
 */
export function runQuery(sql: string, params: unknown[] = []): Promise<sqlite3.RunResult> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.run(sql, params, function(err) {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(this);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Get a single row from a query
 */
export function getRow<T = unknown>(sql: string, params: unknown[] = []): Promise<T | undefined> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.get(sql, params, (err, row) => {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(row as T);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Get all rows from a query
 */
export function getAllRows<T = unknown>(sql: string, params: unknown[] = []): Promise<T[]> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(rows as T[]);
      });
    } catch (error) {
      reject(error);
    }
  });
}

// User-related database operations
export interface User {
  id: number;
  username: string;
  password: string;
  recovery_key?: string;
  created_at: string;
}

/**
 * Create a new user
 */
export async function createUser(username: string, hashedPassword: string, recoveryKey?: string): Promise<number> {
  const result = await runQuery(
    'INSERT INTO users (username, password, recovery_key) VALUES (?, ?, ?)',
    [username, hashedPassword, recoveryKey]
  );
  
  return result.lastID!;
}

/**
 * Get a user by username
 */
export async function getUserByUsername(username: string): Promise<User | undefined> {
  return await getRow<User>('SELECT * FROM users WHERE username = ?', [username]);
}

/**
 * Get a user by ID
 */
export async function getUserById(id: number): Promise<User | undefined> {
  return await getRow<User>('SELECT * FROM users WHERE id = ?', [id]);
}

/**
 * Update user password
 */
export async function updateUserPassword(id: number, hashedPassword: string): Promise<void> {
  await runQuery('UPDATE users SET password = ? WHERE id = ?', [hashedPassword, id]);
}

/**
 * Update user recovery key
 */
export async function updateUserRecoveryKey(id: number, recoveryKey: string): Promise<void> {
  await runQuery('UPDATE users SET recovery_key = ? WHERE id = ?', [recoveryKey, id]);
}

/**
 * Get all users (for admin purposes)
 */
export async function getAllUsers(): Promise<User[]> {
  return await getAllRows<User>('SELECT * FROM users ORDER BY created_at DESC');
}

/**
 * Delete a user
 */
export async function deleteUser(id: number): Promise<void> {
  await runQuery('DELETE FROM users WHERE id = ?', [id]);
}

// Template-related database operations
export interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders?: string;
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
}

/**
 * Create a new template
 */
export async function createTemplate(
  templateName: string,
  description: string | undefined,
  filename: string,
  placeholders: string | undefined,
  layoutSize: string | undefined,
  userId: number
): Promise<number> {
  const result = await runQuery(
    'INSERT INTO templates (template_name, description, filename, placeholders, layout_size, user_id) VALUES (?, ?, ?, ?, ?, ?)',
    [templateName, description, filename, placeholders, layoutSize, userId]
  );

  return result.lastID!;
}

/**
 * Get a template by ID
 */
export async function getTemplateById(id: number): Promise<Template | undefined> {
  return await getRow<Template>('SELECT * FROM templates WHERE id = ?', [id]);
}

/**
 * Get all templates for a specific user
 */
export async function getTemplatesByUserId(userId: number): Promise<Template[]> {
  return await getAllRows<Template>('SELECT * FROM templates WHERE user_id = ? ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Get all templates (for admin purposes)
 */
export async function getAllTemplates(): Promise<Template[]> {
  return await getAllRows<Template>('SELECT * FROM templates ORDER BY uploaded_at DESC');
}

/**
 * Update template information
 */
export async function updateTemplate(
  id: number,
  templateName: string,
  description: string | undefined,
  placeholders: string | undefined,
  layoutSize: string | undefined
): Promise<void> {
  await runQuery(
    'UPDATE templates SET template_name = ?, description = ?, placeholders = ?, layout_size = ? WHERE id = ?',
    [templateName, description, placeholders, layoutSize, id]
  );
}

/**
 * Delete a template
 */
export async function deleteTemplate(id: number): Promise<void> {
  await runQuery('DELETE FROM templates WHERE id = ?', [id]);
}

/**
 * Get templates with user information (JOIN query)
 */
export async function getTemplatesWithUserInfo(): Promise<(Template & { username: string })[]> {
  return await getAllRows<Template & { username: string }>(
    'SELECT t.*, u.username FROM templates t JOIN users u ON t.user_id = u.id ORDER BY t.uploaded_at DESC'
  );
}

// Initialize database on module load for Next.js
if (typeof window === 'undefined') {
  // Only initialize on server-side
  initializeDatabase().catch(console.error);
}
