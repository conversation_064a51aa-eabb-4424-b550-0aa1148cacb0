"use client";

import Image from "next/image";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { ThemeToggle } from "@/components/theme-toggle";
import { NotificationButton } from "@/components/notification-button";

export function Header() {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
      <SidebarTrigger className="-ml-1" />
      <Separator orientation="vertical" className="mr-2 h-4" />
      <div className="flex flex-1 items-center justify-between">
        <div className="flex items-center gap-3">
          <Image
            src="/images/LDIS.png"
            alt="LDIS Logo"
            width={32}
            height={32}
            className="h-8 w-8"
          />
          <h1 className="text-lg font-semibold">
            Legal Document Issuance System
          </h1>
        </div>
        <div className="flex items-center gap-2">
          <NotificationButton />
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}
